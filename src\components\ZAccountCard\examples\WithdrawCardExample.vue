<template>
  <!-- 使用基础组件重构 ZWithdrawAccountCard 的示例 -->
  <ZBaseAccountCard
    :item="cardInfo"
    :card-width="cardWidth"
    :card-height="cardHeight"
    :left-icon="cardInfo.icon"
    :left-icon-size="35"
    :left-icon-padding="5"
    :show-bg-icon="true"
    :bg-icon-name="cardInfo.name.toLocaleLowerCase()"
    :show-right-area="true"
    :show-bottom-area="true"
    content-layout="vertical"
    :clickable="status !== Status.VIEW"
    @click="handleClickCard"
  >
    <!-- 主要内容区域：显示卡片名称 -->
    <template #content="{ item }">
      <div class="card-header-content">
        <span class="card-name">{{ item.name }}</span>
      </div>
    </template>

    <!-- 右侧区域：根据状态显示不同图标 -->
    <template #right="{ item }">
      <template v-if="status === Status.CHECK">
        <CheckedUnCheckedIcon
          :disabled="!activeChannel"
          :type="item.name"
          :isChecked="checkedAccountId === item.account_id"
        />
      </template>
      <ZIcon v-if="status === Status.EDIT" type="icon-bianji1" color="#fff" />
    </template>

    <!-- 底部区域：显示卡号 -->
    <template #bottom="{ item }">
      <div class="card-footer">
        <span class="card-num">{{ formatCardNumber(item.account_no) }}</span>
      </div>
    </template>
  </ZBaseAccountCard>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo, METHODS_NAMES } from "@/utils/config/GlobalConstant";
import { showToast } from "vant";
import { useWithdrawStore } from "@/stores/withdraw";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import ZIcon from "@/components/ZIcon/index.vue";
import ZBaseAccountCard from "../ZBaseAccountCard.vue";
import { Status, type Item } from "../types";

const emits = defineEmits(["click"]);

const props = defineProps({
  item: {
    type: Object as () => Item,
    required: true,
    default: () => ({}),
  },
  status: {
    type: String as () => Status,
    default: Status.VIEW,
    validator: (value: string) => Object.values(Status).includes(value as Status),
  },
  checkedAccountId: {
    type: String,
    default: "",
  },
  cardWidth: {
    type: Number,
    default: 0,
  },
  cardHeight: {
    type: Number,
    default: 114,
  },
});

const activeChannel = computed(() => {
  const withdrawStore = useWithdrawStore();
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item: any) => item.account_type === props.item.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item?.type);
  return {
    ...props.item,
    name: base.name,
    icon: activeChannel.value?.icon,
  };
});

const handleClickCard = (item: any) => {
  if (props.status === Status.VIEW) return;
  if (!activeChannel.value) {
    showToast(
      `${METHODS_NAMES[props.item.type]} cannot withdraw, please switch to other withdrawal methods`
    );
    return;
  }
  emits("click", item);
};
</script>

<style scoped lang="scss">
.card-header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  .card-name {
    color: #fff;
    font-size: 18px;
    font-weight: 700;
  }
}

.card-footer {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  width: 100%;
  justify-content: center;

  .card-num {
    color: #fff;
    text-align: center;
    font-family: "D-DIN";
    font-size: 28px;
    font-weight: 700;
    line-height: normal;
  }
}
</style>
