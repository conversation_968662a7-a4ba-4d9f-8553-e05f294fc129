<template>
  <ZBaseAccountCard
    :item="cardInfo"
    :card-width="cardWidth"
    :card-height="cardHeight"
    :left-icon="cardInfo.icon"
    :left-icon-size="35"
    :left-icon-padding="5"
    :show-bg-icon="true"
    :bg-icon-name="cardInfo.name.toLocaleLowerCase()"
    :show-right-area="status !== Status.VIEW"
    :show-bottom-area="true"
    content-layout="vertical-inline"
    :clickable="status !== Status.VIEW"
    :extra-classes="['withdraw-card']"
    @click="handleClickCard"
  >
    <!-- 主要内容区域：显示卡片名称 -->
    <template #content="{ item }">
      <div class="withdraw-card-header">
        <div class="card-logo-wrapper">
          <WithdrawTypeIcon :icon="cardInfo.icon" :width="35" :height="35" :padding="5" />
        </div>
        <span class="card-name">{{ item.name }}</span>
      </div>
    </template>

    <!-- 右侧区域：编辑和选择按钮 -->
    <template #right="{ theme }">
      <template v-if="status === Status.CHECK">
        <CheckedUnCheckedIcon
          :color="theme.checkBoxColor"
          :disabled="!activeChannel"
          :type="cardInfo.name"
          :isChecked="checkedAccountId === item.account_id"
          :size="20"
        />
      </template>
      <ZIcon v-if="status === Status.EDIT" type="icon-bianji1" color="#fff" />
    </template>

    <!-- 底部区域：显示卡号 -->
    <template #bottom>
      <div class="withdraw-card-footer">
        <span class="card-num">{{ formatCardNumber(item.account_no) }}</span>
      </div>
    </template>
  </ZBaseAccountCard>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatCardNumber } from "@/utils/core/tools";
import { getMethodsInfo, METHODS_NAMES } from "@/utils/config/GlobalConstant";
import { showToast } from "vant";
import { useWithdrawStore } from "@/stores/withdraw";
import ZBaseAccountCard from "./ZBaseAccountCard.vue";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";
import CheckedUnCheckedIcon from "@/components/ZComonImg/CheckedUnCheckedIcon.vue";
import ZIcon from "@/components/ZIcon/index.vue";
import { Status, type Item } from "./types";

const emits = defineEmits(["click"]);

const props = defineProps({
  item: {
    type: Object as () => Item,
    required: true,
    default: () => ({}),
  },
  status: {
    type: String as () => Status,
    default: Status.VIEW,
    validator: (value: string) => Object.values(Status).includes(value as Status),
  },
  checkedAccountId: {
    type: String,
    default: "",
  },
  cardWidth: {
    type: Number,
    default: 0,
  },
  cardHeight: {
    type: Number,
    default: 114,
  },
});

const activeChannel = computed(() => {
  const withdrawStore = useWithdrawStore();
  const list = withdrawStore.withdrawData || [];
  const activeChannel = list.find((item: any) => item.account_type === props.item.type);
  return activeChannel;
});

const cardInfo = computed(() => {
  const base = getMethodsInfo(props.item?.type);
  console.log("base", base);
  return {
    ...props.item,
    name: base.name,
    icon: activeChannel.value?.icon,
  };
});

// cardTheme, cardStyle, shadowStyle 现在由 ZBaseAccountCard 组件处理

const handleClickCard = () => {
  if (props.status === Status.VIEW) return;
  if (!activeChannel.value) {
    showToast(
      `${METHODS_NAMES[props.item.type]} cannot withdraw, please switch to other withdrawal methods`
    );
    return;
  }
  emits("click", props.item);
};
</script>

<style scoped lang="scss">
// 特定于 withdraw 卡片的样式覆盖
:deep(.withdraw-card) {
  border-radius: 20px; // 保持原有的圆角样式
}

.withdraw-card-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  gap: 8px;

  .card-logo-wrapper {
    flex-shrink: 0;
  }

  .card-name {
    color: #fff;
    font-size: 18px;
    font-weight: 700;
    flex: 1;
  }
}

.withdraw-card-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  width: 100%;

  .card-num {
    color: #fff;
    text-align: center;
    font-family: "D-DIN";
    font-size: 28px;
    font-weight: 700;
    line-height: normal;
  }
}
</style>
