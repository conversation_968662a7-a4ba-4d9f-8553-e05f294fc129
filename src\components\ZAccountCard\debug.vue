<template>
  <div class="debug-container">
    <h2>ZWithdrawAccountCard 调试页面</h2>
    
    <div class="debug-info">
      <h3>测试数据</h3>
      <pre>{{ JSON.stringify(testItem, null, 2) }}</pre>
    </div>
    
    <div class="card-test">
      <h3>组件渲染测试</h3>
      <div class="card-wrapper">
        <ZWithdrawAccountCard
          :item="testItem"
          status="edit"
          :card-width="300"
          :card-height="134"
          @click="handleCardClick"
        />
      </div>
    </div>
    
    <div class="original-test">
      <h3>原始样式对比</h3>
      <div class="original-card">
        <div class="wrap">
          <div class="card-shadow" :style="shadowStyle"></div>
          <div class="card-wrap" :style="cardStyle">
            <div class="bg-logo">
              <div class="icon">🏦</div>
            </div>
            <div class="card-header">
              <div class="left">
                <div class="card-logo">💳</div>
                <span class="card-name">GCash</span>
              </div>
              <div class="right">✏️</div>
            </div>
            <div class="card-footer">
              <span class="card-num">**** **** **** 1234</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import ZWithdrawAccountCard from './ZWithdrawAccountCard.vue';

const testItem = ref({
  account_id: "test-123",
  account_no: "****************",
  type: 12, // GCash
  name: "GCash"
});

const handleCardClick = (item: any) => {
  console.log('Card clicked:', item);
};

// 模拟原始样式
const shadowStyle = computed(() => ({
  width: "284px",
  height: "134px",
  boxShadow: "0 4px 8px 0 #39B0FF66",
}));

const cardStyle = computed(() => ({
  background: "linear-gradient(90deg, #39B0FF 0%, #4881ED 100%)",
  color: "#fff",
  width: "300px",
  height: "134px",
}));
</script>

<style scoped lang="scss">
.debug-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
  font-family: Arial, sans-serif;
}

.debug-info {
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  pre {
    background: #f8f8f8;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
  }
}

.card-test, .original-test {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.card-wrapper {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f0f0f0;
  border-radius: 8px;
}

// 原始样式重现
.original-card {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f0f0f0;
  border-radius: 8px;
  
  .wrap {
    position: relative;
    background: transparent;
    padding: 0;
    margin: 0;
    transition: all 0.2s ease;
    
    .card-shadow {
      position: absolute;
      border-radius: 16px;
      top: 0;
      left: 8px;
      z-index: 1;
    }

    &:hover {
      transform: translateY(-1px);
    }
    
    .bg-logo {
      position: absolute;
      right: 8px;
      top: 0;
      bottom: 0;
      color: #fff;
      opacity: 0.1;
      display: flex;
      align-items: center;
      font-size: 60px;
    }
  }

  .card-wrap {
    font-family: "Inter";
    width: 100%;
    border-radius: 20px;
    color: #fff;
    padding: 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    position: relative;
    z-index: 5;
    gap: 12px;

    .card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .left {
        display: flex;
        align-items: center;
        gap: 8px;

        .card-name {
          color: #fff;
          font-size: 18px;
          font-weight: 700;
        }
      }
    }

    .card-footer {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: bold;

      .card-num {
        color: #fff;
        text-align: center;
        font-family: "D-DIN";
        font-size: 28px;
        font-weight: 700;
        line-height: normal;
      }
    }
  }
}

h2, h3 {
  color: #333;
  margin: 0 0 15px 0;
}
</style>
