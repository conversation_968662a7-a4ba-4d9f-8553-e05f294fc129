<template>
  <!-- 账户卡片基础样式组件 -->
  <div class="wrap">
    <!-- 卡片底部阴影 -->
    <div class="card-shadow" :style="shadowStyle"></div>
    <!-- 卡片内容 -->
    <div class="base-card" :class="cardClasses" :style="cardStyle" @click="handleClick">
      <!-- 背景装饰图标（可选） -->
      <div v-if="showBgIcon" class="bg-logo">
        <ZIcon :type="`icon-${bgIconName}`" :size="95" />
      </div>

      <!-- 左侧固定图标区域 -->
      <div class="left-icon-area">
        <WithdrawTypeIcon
          :icon="leftIcon"
          :width="leftIconSize"
          :height="leftIconSize"
          :padding="leftIconPadding"
        />
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content" :class="contentLayoutClass">
        <slot name="content" :item="item" :theme="cardTheme">
          <!-- 默认内容：显示名称 -->
          <div class="default-content">
            <span class="card-name">{{ item.name }}</span>
          </div>
        </slot>
      </div>

      <!-- 右侧可配置区域 -->
      <div v-if="showRightArea" class="right-area">
        <slot name="right" :item="item" :theme="cardTheme" :is-selected="isSelected">
          <!-- 默认右侧内容 -->
        </slot>
      </div>

      <!-- 底部可配置区域 -->
      <div v-if="showBottomArea" class="bottom-area">
        <slot name="bottom" :item="item" :theme="cardTheme">
          <!-- 默认底部内容 -->
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import WithdrawTypeIcon from "@/components/ZComonImg/WithdrawTypeIcon.vue";
import ZIcon from "@/components/ZIcon/index.vue";
import { CARD_THEMES } from "./types";

// 基础账户项目接口
interface BaseAccountItem {
  name: string;
  icon?: string;
  [key: string]: any;
}

interface Props {
  /** 账户项目数据 */
  item: BaseAccountItem;
  /** 卡片宽度 */
  cardWidth?: number;
  /** 卡片高度 */
  cardHeight?: number;
  /** 左侧图标 */
  leftIcon?: string;
  /** 左侧图标大小 */
  leftIconSize?: number;
  /** 左侧图标内边距 */
  leftIconPadding?: number;
  /** 是否显示背景装饰图标 */
  showBgIcon?: boolean;
  /** 背景图标名称 */
  bgIconName?: string;
  /** 是否显示右侧区域 */
  showRightArea?: boolean;
  /** 是否显示底部区域 */
  showBottomArea?: boolean;
  /** 内容布局方式：horizontal(水平) | vertical(垂直) | vertical-inline(垂直内联) */
  contentLayout?: "horizontal" | "vertical" | "vertical-inline";
  /** 是否选中状态 */
  isSelected?: boolean;
  /** 是否可点击 */
  clickable?: boolean;
  /** 额外的CSS类 */
  extraClasses?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  cardWidth: 0,
  cardHeight: 114,
  leftIconSize: 35,
  leftIconPadding: 5,
  showBgIcon: false,
  bgIconName: "",
  showRightArea: false,
  showBottomArea: false,
  contentLayout: "vertical",
  isSelected: false,
  clickable: true,
  extraClasses: () => [],
});

const emits = defineEmits<{
  click: [item: BaseAccountItem];
}>();

// 计算属性
const cardTheme = computed(() => {
  return CARD_THEMES[props.item.name?.toLocaleLowerCase()] || CARD_THEMES.default;
});

const cardClasses = computed(() => {
  const classes = [`layout-${props.contentLayout}`, ...props.extraClasses];

  if (props.isSelected) {
    classes.push("is-selected");
  }

  if (props.clickable) {
    classes.push("is-clickable");
  }

  return classes;
});

const contentLayoutClass = computed(() => ({
  "horizontal-layout": props.contentLayout === "horizontal",
  "vertical-layout": props.contentLayout === "vertical",
  "vertical-inline-layout": props.contentLayout === "vertical-inline",
}));

const shadowStyle = computed(() => ({
  width: props.cardWidth ? `${props.cardWidth - 16}px` : "calc(100% - 16px)",
  height: `${props.cardHeight}px`,
  boxShadow: cardTheme.value.boxShadow,
}));

const cardStyle = computed(() => ({
  background: cardTheme.value.background,
  color: cardTheme.value.textColor,
  width: props.cardWidth ? `${props.cardWidth}px` : "100%",
  height: `${props.cardHeight}px`,
}));

// 方法
const handleClick = () => {
  if (!props.clickable) return;
  emits("click", props.item);
};
</script>

<style scoped lang="scss">
.wrap {
  position: relative;
  background: transparent;
  padding: 0;
  margin: 0;
  transition: all 0.2s ease;

  .card-shadow {
    position: absolute;
    border-radius: 16px;
    top: 0;
    left: 8px;
    z-index: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }

  .bg-logo {
    position: absolute;
    right: 8px;
    top: 0;
    bottom: 0;
    color: #fff;
    opacity: 0.1;
    display: flex;
    align-items: center;
  }
}

.base-card {
  font-family: "Inter";
  width: 100%;
  border-radius: 16px;
  color: #fff;
  padding: 16px;
  box-sizing: border-box;
  display: flex;
  position: relative;
  z-index: 5;
  overflow: hidden;

  &.is-clickable {
    cursor: pointer;
  }

  &.is-selected {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  // 垂直布局
  &.layout-vertical {
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 12px;

    .left-icon-area {
      position: absolute;
      top: 16px;
      left: 16px;
    }

    .main-content {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &.vertical-layout {
        .default-content {
          text-align: center;

          .card-name {
            color: #fff;
            font-size: 18px;
            font-weight: 700;
          }
        }
      }
    }

    .right-area {
      position: absolute;
      top: 16px;
      right: 16px;
    }

    .bottom-area {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 垂直内联布局（图标和内容在同一行，但整体垂直排列）
  &.layout-vertical-inline {
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    gap: 12px;

    .left-icon-area {
      display: none; // 隐藏左侧图标区域，因为图标会在内容区域显示
    }

    .main-content {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &.vertical-inline-layout {
        .default-content {
          text-align: center;

          .card-name {
            color: #fff;
            font-size: 18px;
            font-weight: 700;
          }
        }
      }
    }

    .right-area {
      position: absolute;
      top: 16px;
      right: 16px;
    }

    .bottom-area {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  // 水平布局
  &.layout-horizontal {
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;

    .left-icon-area {
      flex-shrink: 0;
    }

    .main-content {
      flex: 1;

      &.horizontal-layout {
        .default-content {
          text-align: left;

          .card-name {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
          }
        }
      }
    }

    .right-area {
      flex-shrink: 0;
    }
  }
}
</style>
