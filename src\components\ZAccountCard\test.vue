<template>
  <div class="test-container">
    <h2>ZWithdrawAccountCard 测试</h2>
    
    <div class="card-grid">
      <div class="card-item">
        <h3>VIEW 状态</h3>
        <ZWithdrawAccountCard
          :item="testItem"
          status="view"
          :card-width="300"
          :card-height="114"
        />
      </div>
      
      <div class="card-item">
        <h3>CHECK 状态</h3>
        <ZWithdrawAccountCard
          :item="testItem"
          status="check"
          :card-width="300"
          :card-height="114"
          :checked-account-id="testItem.account_id"
        />
      </div>
      
      <div class="card-item">
        <h3>EDIT 状态</h3>
        <ZWithdrawAccountCard
          :item="testItem"
          status="edit"
          :card-width="300"
          :card-height="114"
        />
      </div>
    </div>

    <h2>ZAccountCard (index.vue) 测试</h2>
    
    <div class="card-grid">
      <div class="card-item">
        <h3>水平布局</h3>
        <ZAccountCard
          :item="testAccountItem"
          :card-width="300"
          :card-height="56"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ZWithdrawAccountCard from './ZWithdrawAccountCard.vue';
import ZAccountCard from './index.vue';

const testItem = ref({
  account_id: "test-123",
  account_no: "****************",
  type: 1,
  name: "GCash"
});

const testAccountItem = ref({
  name: "GCash",
  icon: "gcash-icon",
  account_type: 1
});
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.card-item {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
  }
}

h2 {
  color: #333;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #eee;
}
</style>
